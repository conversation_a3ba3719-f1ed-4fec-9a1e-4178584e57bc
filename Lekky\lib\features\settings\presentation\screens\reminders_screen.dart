import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../core/models/settings_state.dart';
import '../../../../core/shared/widgets/settings_section_header.dart';
import '../../../../core/services/reminder_scheduling_service.dart';
import '../../../../core/utils/date_formatter.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/app_banner.dart';

/// Reminders settings screen
class RemindersScreen extends ConsumerWidget {
  /// Constructor
  const RemindersScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsAsync = ref.watch(settingsProvider);

    return Scaffold(
      body: settingsAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 48, color: Colors.red),
              const SizedBox(height: 16),
              Text('Error loading settings: $error'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.refresh(settingsProvider),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
        data: (settings) => Column(
          children: [
            // Banner with back arrow
            GestureDetector(
              onTap: () => Navigator.of(context).pop(),
              child: AppBanner(
                message: '← Reminders',
                gradientColors: AppColors.getSettingsMainCardGradient(
                    Theme.of(context).brightness == Brightness.dark),
                textColor: AppColors.getAppBarTextColor('settings',
                    Theme.of(context).brightness == Brightness.dark),
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Card(
                      margin: const EdgeInsets.all(8.0),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.0),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SettingsSectionHeader(
                              title: 'Reminders',
                              description:
                                  'Configure when you want to receive reminders.',
                              icon: Icons.alarm,
                            ),
                            const SizedBox(height: 16),

                            // Enable Reminders
                            SwitchListTile(
                              title: const Text('Enable Reminders'),
                              subtitle: const Text(
                                  'Get regular reminders to check your meter.'),
                              value: settings.remindersEnabled,
                              onChanged: (value) {
                                ref
                                    .read(settingsProvider.notifier)
                                    .updateRemindersEnabled(value);
                              },
                            ),

                            const Divider(),

                            // Reminder Frequency
                            ListTile(
                              title: const Text('Reminder Frequency'),
                              subtitle: Text(settings.reminderFrequency),
                              trailing:
                                  const Icon(Icons.arrow_forward_ios, size: 16),
                              onTap: () {
                                _showReminderFrequencyDialog(
                                    context, ref, settings);
                              },
                            ),

                            // Reminder Schedule (only show if reminders enabled)
                            if (settings.remindersEnabled) ...[
                              const Divider(),
                              _buildReminderScheduleButton(
                                  context, ref, settings),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showReminderFrequencyDialog(
      BuildContext context, WidgetRef ref, SettingsState settings) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Reminder Frequency'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              RadioListTile<String>(
                title: const Text('Daily'),
                value: 'daily',
                groupValue: settings.reminderFrequency,
                onChanged: (value) {
                  Navigator.pop(context);
                  if (value != null) {
                    ref
                        .read(settingsProvider.notifier)
                        .updateReminderFrequency(value);
                  }
                },
              ),
              RadioListTile<String>(
                title: const Text('Weekly'),
                value: 'weekly',
                groupValue: settings.reminderFrequency,
                onChanged: (value) {
                  Navigator.pop(context);
                  if (value != null) {
                    ref
                        .read(settingsProvider.notifier)
                        .updateReminderFrequency(value);
                  }
                },
              ),
              RadioListTile<String>(
                title: const Text('Bi-weekly'),
                value: 'bi-weekly',
                groupValue: settings.reminderFrequency,
                onChanged: (value) {
                  Navigator.pop(context);
                  if (value != null) {
                    ref
                        .read(settingsProvider.notifier)
                        .updateReminderFrequency(value);
                  }
                },
              ),
              RadioListTile<String>(
                title: const Text('Monthly'),
                value: 'monthly',
                groupValue: settings.reminderFrequency,
                onChanged: (value) {
                  Navigator.pop(context);
                  if (value != null) {
                    ref
                        .read(settingsProvider.notifier)
                        .updateReminderFrequency(value);
                  }
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  /// Build reminder schedule section with upcoming reminders
  Widget _buildReminderScheduleButton(
      BuildContext context, WidgetRef ref, SettingsState settings) {
    final startDateTime = settings.reminderStartDateTime;
    final frequency = settings.reminderFrequency;

    return Column(
      children: [
        ListTile(
          title: const Text('Reminder Schedule'),
          subtitle:
              Text(_getScheduleDescription(context, startDateTime, frequency)),
          trailing: const Icon(Icons.schedule),
          onTap: () => _showReminderDateTimePicker(context, ref, settings),
        ),
        if (startDateTime != null) ...[
          const Divider(height: 1),
          _buildUpcomingReminders(context, settings),
        ],
      ],
    );
  }

  /// Get schedule description text
  String _getScheduleDescription(
      BuildContext context, DateTime? startDateTime, String frequency) {
    if (startDateTime == null) {
      return 'Set Reminder Time';
    } else if (frequency == 'daily') {
      return 'Daily at ${TimeOfDay.fromDateTime(startDateTime).format(context)}';
    } else {
      final dateFormat = DateFormat('MMM d, y');
      final timeFormat = TimeOfDay.fromDateTime(startDateTime);
      return '${frequency.substring(0, 1).toUpperCase()}${frequency.substring(1)} starting ${dateFormat.format(startDateTime)} at ${timeFormat.format(context)}';
    }
  }

  /// Build upcoming reminders display
  Widget _buildUpcomingReminders(BuildContext context, SettingsState settings) {
    return FutureBuilder<List<DateTime>>(
      future: ReminderSchedulingService().getUpcomingReminders(count: 3),
      builder: (context, snapshot) {
        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return const SizedBox.shrink();
        }

        final upcomingReminders = snapshot.data!;

        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Upcoming Reminders',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).colorScheme.primary,
                    ),
              ),
              const SizedBox(height: 4),
              Text(
                'Note: Reminders won\'t fire if you\'ve already taken a reading after 3 AM the same day',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontSize: 11,
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                      fontStyle: FontStyle.italic,
                    ),
              ),
              const SizedBox(height: 8),
              ...upcomingReminders.take(3).map(
                    (reminder) => _buildReminderItem(
                        context, reminder, upcomingReminders.indexOf(reminder)),
                  ),
            ],
          ),
        );
      },
    );
  }

  /// Build individual reminder item
  Widget _buildReminderItem(
      BuildContext context, DateTime reminder, int index) {
    final now = DateTime.now();
    final isToday = reminder.year == now.year &&
        reminder.month == now.month &&
        reminder.day == now.day;
    final isTomorrow = reminder.difference(now).inDays == 1;

    String dateText;
    if (isToday) {
      dateText = 'Today';
    } else if (isTomorrow) {
      dateText = 'Tomorrow';
    } else {
      dateText = DateFormatter.formatDateForDashboard(reminder);
    }

    final timeText = TimeOfDay.fromDateTime(reminder).format(context);
    final label = index == 0
        ? 'Next:'
        : index == 1
            ? 'Following:'
            : 'Then:';

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0),
      child: Row(
        children: [
          SizedBox(
            width: 70,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.7),
                  ),
            ),
          ),
          Text(
            '$dateText at $timeText',
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],
      ),
    );
  }

  /// Show reminder date/time picker based on frequency
  Future<void> _showReminderDateTimePicker(
      BuildContext context, WidgetRef ref, SettingsState settings) async {
    final frequency = settings.reminderFrequency;
    final currentDateTime = settings.reminderStartDateTime ?? DateTime.now();

    if (frequency == 'daily') {
      await _showTimeOnlyPicker(context, ref, currentDateTime);
    } else {
      await _showDateTimePicker(context, ref, currentDateTime);
    }
  }

  /// Show time-only picker for daily reminders
  Future<void> _showTimeOnlyPicker(
      BuildContext context, WidgetRef ref, DateTime current) async {
    final TimeOfDay? pickedTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.fromDateTime(current),
    );

    if (pickedTime != null) {
      final now = DateTime.now();
      var newDateTime = DateTime(
        now.year,
        now.month,
        now.day,
        pickedTime.hour,
        pickedTime.minute,
      );

      // If the time has already passed today, schedule for tomorrow
      if (newDateTime.isBefore(now)) {
        newDateTime = newDateTime.add(const Duration(days: 1));
      }

      await ref
          .read(settingsProvider.notifier)
          .updateReminderStartDateTime(newDateTime);
    }
  }

  /// Show date+time picker for non-daily reminders
  Future<void> _showDateTimePicker(
      BuildContext context, WidgetRef ref, DateTime current) async {
    final now = DateTime.now();

    // First pick date
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: current.isAfter(now) ? current : now,
      firstDate: now,
      lastDate: now.add(const Duration(days: 365)),
    );

    if (pickedDate != null && context.mounted) {
      // Then pick time
      final TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(current),
      );

      if (pickedTime != null) {
        var newDateTime = DateTime(
          pickedDate.year,
          pickedDate.month,
          pickedDate.day,
          pickedTime.hour,
          pickedTime.minute,
        );

        // Ensure the datetime is in the future
        if (newDateTime.isBefore(now)) {
          newDateTime = DateTime(
            now.year,
            now.month,
            now.day,
            pickedTime.hour,
            pickedTime.minute,
          ).add(const Duration(days: 1));
        }

        await ref
            .read(settingsProvider.notifier)
            .updateReminderStartDateTime(newDateTime);
      }
    }
  }
}
